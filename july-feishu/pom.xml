<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.july.app</groupId>
        <artifactId>july-orch-meeting</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>july-feishu</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-database</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-event-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-cache</artifactId>
        </dependency>

        <!-- 飞书SDK -->
        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>2.4.12</version>
            <optional>true</optional>
        </dependency>
    </dependencies>


</project>