knife4j:
  enable: true
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://**********:6379",
            "password": "dajf780redis@a88",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
dromara:
  x-file-storage: #文件存储配置
    default-platform: minio-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: FHZArYPFxUOjadtHNGbo
        secret-key: iSjOzz2RB1ibZ2jV4WpOi2Tolp7kTZNfPDfB18rg
        end-point: http://coe-file-sit.pengfeijituan.com:9000/
        bucket-name: meiye-safe
        domain: http://coe-file-sit.pengfeijituan.com:9000/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: ${spring.application.name}/
feishu:
  mini:
    appId: cli_a779c00b87b6500c
    appSecret: JMTJnSfGADn1AvZ1jaRn4gZOxxTD08ZF
july:
  database:
    multi:
      db:
        pf_orch_meiye:
          primary: true
          master:
            jdbcUrl: ************************************************************************************************************************************************
            username: meiye
            password: pf123456.
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10