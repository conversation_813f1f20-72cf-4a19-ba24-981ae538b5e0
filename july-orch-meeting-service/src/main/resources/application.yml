server:
  port: 8102
  servlet:
    context-path: /api/meeting
  tomcat:
    connection-timeout: 60000
    keep-alive-timeout: 5000
spring:
  profiles:
    active: @spring.profiles.active@
  application:
    name: july-orch-meeting
  servlet:
    multipart:
      resolve-lazily: true # multipart 懒加载
      max-file-size: 20MB
      max-request-size: 20MB
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  mapper-locations: classpath:mapper/*Mapper.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

knife4j:
  enable: true
  openapi:
    title: ${spring.application.name}
    description: 会议系统
    concat: july
    version: 1.0.0

july:
  database:
    mybatis-plus:
      enable-tenant-line: false
  spring:
    web:
      exception:
        # 需要自定义修改
        product-code: "01"
        service-code: "01"
      access-log:
        enabled: true
        global: true
      request-context-type: cn.july.orch.meeting.config.JulyRequestContext